"""
Simple historical data service matching TypeScript implementation.

This service provides K-line data fetching with straightforward logic.
"""

import asyncio
import json
from typing import List, Dict, Optional
from datetime import datetime, timezone
from dataclasses import dataclass

from ..clients.exchange_api_client_v2 import ExchangeAPIClientV2, ExchangeAPIError
from ..storage.kline_storage import KlineStorage
from ..models.kline import KlineData
from ..utils.logger import get_logger


@dataclass
class FetchResult:
    """Result of historical data fetch operation."""
    symbol: str
    interval: str
    klines_fetched: int
    success: bool
    error: Optional[str] = None


class HistoricalDataService:
    """
    Service for fetching historical K-line data.
    
    Simple implementation that fetches the most recent N K-lines.
    """
    
    def __init__(self, 
                 exchange_client: Optional[ExchangeAPIClientV2] = None,
                 storage: Optional[KlineStorage] = None,
                 settings = None):
        """
        Initialize historical data service.
        
        Args:
            exchange_client: Exchange API client instance
            storage: K-line storage instance
            settings: Application settings
        """
        self.exchange_client = exchange_client or ExchangeAPIClientV2()
        self.storage = storage or KlineStorage()
        self.logger = get_logger(self.__class__.__name__)
        
        # Import here to avoid circular imports
        from ..config.settings import Settings
        self.settings = settings or Settings()
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close the service and cleanup resources."""
        try:
            await self.exchange_client.close()
            self.logger.info("Historical data service closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing historical data service: {e}")
    
    async def sync_klines(self,
                         symbol: str,
                         interval: str = "1m",
                         target_count: int = 1000,
                         count: int = 5) -> FetchResult:
        """
        Sync K-lines - fetch historical data in multiple iterations.

        Args:
            symbol: Trading symbol
            interval: Time interval (default: 1m)
            target_count: Total number of K-lines to fetch across all iterations
            count: Number of iterations to perform

        Returns:
            FetchResult with sync statistics
        """
        try:
            # Calculate total target and batch size per iteration
            total_target = target_count * count  # Total K-lines to fetch
            batch_size = min(target_count, 1500)  # K-lines per iteration, limited by API

            self.logger.info(f"Starting sync for {symbol}:{interval} - {count} iterations of {target_count} klines each (total: {total_target})")
            self.logger.info(f"Settings: historical_rate_limit_delay={self.settings.historical_rate_limit_delay}")

            total_fetched = 0
            total_stored = 0
            end_time = None
            current_count = count
            
            # Loop through iterations like the TypeScript code
            while current_count > 0:
                iteration_num = count - current_count + 1
                self.logger.info(f"🔄 Starting iteration {iteration_num}/{count} for {symbol}:{interval}, current_count={current_count}")

                # Fetch K-lines
                klines = await self.exchange_client.get_continuous_klines(
                    symbol=symbol,
                    interval=interval,
                    limit=batch_size,
                    end_time=end_time
                )

                if not klines or len(klines) == 0:
                    self.logger.warning(f"❌ No data returned for {symbol}:{interval} iteration {iteration_num}")
                    break

                self.logger.info(f"✅ Successfully fetched {len(klines)} records for {symbol} {interval} iteration {iteration_num}")
                
                # Debug: Check data type
                if klines:
                    self.logger.debug(f"First kline type: {type(klines[0])}")
                    if hasattr(klines[0], '__dict__'):
                        self.logger.debug(f"First kline attributes: {klines[0].__dict__}")
                
                # Process and store klines
                self.logger.info(f"📦 Processing {len(klines)} klines for iteration {iteration_num}")
                stored_in_iteration = 0

                for i, kline in enumerate(klines):
                    try:
                        # Convert kline format if needed
                        # Check if kline is a KlineData object or dict
                        if hasattr(kline, 't'):
                            # It's a KlineData object
                            kline_data = {
                                't': kline.t,
                                'T': kline.T,
                                's': symbol,
                                'i': interval,
                                'f': 0,
                                'L': 0,
                                'o': kline.o,
                                'c': kline.c,
                                'h': kline.h,
                                'l': kline.l,
                                'v': kline.v,
                                'n': kline.n,
                                'x': kline.x,
                                'q': kline.q,
                                'V': kline.V,
                                'Q': kline.Q,
                                'B': "0"
                            }
                            open_time = kline.t
                        else:
                            # It's a dict
                            kline_data = {
                                't': kline['open_time'],
                                'T': kline['close_time'],
                                's': symbol,
                                'i': interval,
                                'f': 0,
                                'L': 0,
                                'o': kline['open'],
                                'c': kline['close'],
                                'h': kline['high'],
                                'l': kline['low'],
                                'v': kline['volume'],
                                'n': kline['trade_count'],
                                'x': kline['is_closed'],
                                'q': kline['quote_volume'],
                                'V': kline['taker_buy_volume'],
                                'Q': kline['taker_buy_quote_volume'],
                                'B': "0"
                            }
                            open_time = kline['open_time']

                        # Store the K-line using the storage service
                        # Convert the kline_data dict to a KlineData object for storage
                        from ..models.kline import KlineData
                        kline_obj = KlineData(**kline_data)
                        await self.storage.store_kline(kline_obj)
                        stored_in_iteration += 1
                        total_stored += 1

                        # Log progress every 500 records
                        if (i + 1) % 500 == 0:
                            self.logger.info(f"📊 Stored {i + 1}/{len(klines)} klines in iteration {iteration_num}")

                    except Exception as e:
                        self.logger.error(f"❌ Error storing kline {i+1} in iteration {iteration_num}: {e}")
                        # Continue with next kline instead of breaking
                        continue

                self.logger.info(f"✅ Iteration {iteration_num} completed: stored {stored_in_iteration} klines")
                
                total_fetched += len(klines)

                # Decrement count and prepare for next iteration
                current_count -= 1
                self.logger.info(f"🔄 Iteration {iteration_num} finished. Remaining iterations: {current_count}")

                if current_count > 0 and len(klines) > 0:
                    # Set end_time to the oldest kline's open time for next iteration
                    # Binance returns data from oldest to newest, so the first element is the oldest
                    oldest_kline = klines[0]  # First element is the oldest
                    if hasattr(oldest_kline, 't'):
                        end_time = oldest_kline.t - 1  # Subtract 1 to avoid duplicate
                        self.logger.info(f"🕐 Next iteration will start from timestamp: {end_time} (oldest kline: {oldest_kline.t})")
                    else:
                        end_time = oldest_kline['open_time'] - 1
                        self.logger.info(f"🕐 Next iteration will start from timestamp: {end_time} (oldest kline: {oldest_kline['open_time']})")

                    # Add delay to avoid rate limits
                    self.logger.info(f"⏳ Waiting {self.settings.historical_rate_limit_delay}s before next iteration...")
                    await asyncio.sleep(self.settings.historical_rate_limit_delay)
                else:
                    if current_count > 0:
                        self.logger.warning(f"⚠️ Stopping early: no more data available (remaining iterations: {current_count})")
                    else:
                        self.logger.info(f"✅ All {count} iterations completed successfully")
            
            self.logger.info(f"Sync completed for {symbol}:{interval}: {total_stored} klines stored")
            
            return FetchResult(
                symbol=symbol,
                interval=interval,
                klines_fetched=total_fetched,
                success=True,
                error=None
            )
            
        except Exception as e:
            import traceback
            self.logger.error(f"Sync failed for {symbol}:{interval}: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return FetchResult(
                symbol=symbol,
                interval=interval,
                klines_fetched=0,
                success=False,
                error=str(e)
            )
    
    async def sync_multiple_symbols(self,
                                  symbols: List[str],
                                  interval: str = "1m",
                                  target_count: int = 1000) -> List[FetchResult]:
        """
        Sync K-lines for multiple symbols.
        
        Args:
            symbols: List of trading symbols
            interval: Time interval
            target_count: Number of K-lines to fetch per symbol
            
        Returns:
            List of fetch results
        """
        results = []
        
        for symbol in symbols:
            result = await self.sync_klines(symbol, interval, target_count=target_count)
            results.append(result)
            
            # Add small delay between symbols to avoid rate limits
            if symbol != symbols[-1]:  # Not the last symbol
                await asyncio.sleep(0.5)
        
        # Log summary
        successful = sum(1 for r in results if r.success)
        total_klines = sum(r.klines_fetched for r in results)
        
        self.logger.info(f"Sync completed: {successful}/{len(symbols)} successful, "
                        f"{total_klines} total K-lines fetched")
        
        return results


# Global service instance
historical_data_service = HistoricalDataService()